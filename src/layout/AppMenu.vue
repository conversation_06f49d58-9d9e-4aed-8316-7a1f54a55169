<script setup lang="ts">
import { ref } from 'vue';

import AppMenuItem from './AppMenuItem.vue';
import { useAuthStore } from '@/entities/auth';

const authStore = useAuthStore();
const model = ref([
    {
        label: 'Home',
        items: [{ label: 'Dashboard', icon: 'pi pi-fw pi-home', to: '/' }]
    },

    {
        label: 'Leads',
        items: [
            {
                label: 'Actions',
                items: [
                    { label: 'Inquired', icon: 'pi pi-fw pi-question-circle', to: '/leads/inquired' },
                    { label: 'Quoted', icon: 'pi pi-fw pi-euro', to: '/leads/quoted' },
                    { label: 'Booked', icon: 'pi pi-fw pi-calendar-times', to: '/leads/booked' },
                    { label: 'Converted', icon: 'pi pi-fw pi-crown', to: '/leads/converted' }
                ]
            },
            {
                label: 'Classifications',
                items: [
                    { label: 'Qualified Leads', icon: 'pi pi-fw pi-thumbs-up', to: '/leads/qualified' },
                    { label: 'Unqualified Leads', icon: 'pi pi-fw pi-thumbs-down', to: '/leads/unqualified' }
                ]
            },
            {
                label: 'More',
                items: [
                    { label: 'Blocked Leads', icon: 'pi pi-fw pi-ban', to: '/leads/irrelevant' },
                    { label: 'All', icon: 'pi pi-fw pi-address-book', to: '/leads/list' }
                ]
            }
        ]
    },
    {
        label: 'Scheduled',
        items: [
            { label: 'Follow-Ups', icon: 'pi pi-fw pi-clock', to: '/followups' },
            { label: 'Lead Analysis', icon: 'pi pi-fw pi-sparkles', to: '/leads/failed' }
        ]
    },
    {
        label: 'Bots / AI',
        items: [
            { label: 'Whatsapp', icon: 'pi pi-fw pi-whatsapp', to: '/bots/whatsapp' },
            { label: 'Telegram', icon: 'pi pi-fw pi-telegram', to: '/bots/telegram' },
            { label: 'Facebook', icon: 'pi pi-fw pi-facebook', to: '/bots/facebook' },
            { label: 'Email', icon: 'pi pi-fw pi-envelope', to: '/bots/email' },
            { label: 'Call', icon: 'pi pi-fw pi-phone', to: '/bots/call' }
        ]
    },
    {
        label: 'Settings',
        items: [
            { label: 'Users', icon: 'pi pi-fw pi-user-plus', to: '/users' },
            { label: 'Tenants', icon: 'pi pi-fw pi-building', to: '/tenants' }
        ]
    }
]);

if (authStore?.user && authStore.user?.email === '<EMAIL>') {
    model.value = [
        {
            label: 'Home',
            items: [{ label: 'Dashboard', icon: 'pi pi-fw pi-home', to: '/' }]
        },

        {
            label: 'Leads',
            items: [
                {
                    label: 'Actions',
                    items: [
                        { label: 'Inquired', icon: 'pi pi-fw pi-question-circle', to: '/leads/inquired' },
                        { label: 'Quoted', icon: 'pi pi-fw pi-euro', to: '/leads/quoted' },
                        { label: 'Booked', icon: 'pi pi-fw pi-calendar-times', to: '/leads/booked' },
                        { label: 'Converted', icon: 'pi pi-fw pi-crown', to: '/leads/converted' }
                    ]
                },
                {
                    label: 'Classifications',
                    items: [
                        { label: 'Qualified Leads', icon: 'pi pi-fw pi-thumbs-up', to: '/leads/qualified' },
                        { label: 'Unqualified Leads', icon: 'pi pi-fw pi-thumbs-down', to: '/leads/unqualified' }
                    ]
                },
                {
                    label: 'More',
                    items: [
                        { label: 'Blocked Leads', icon: 'pi pi-fw pi-ban', to: '/leads/irrelevant' },
                        { label: 'All', icon: 'pi pi-fw pi-address-book', to: '/leads/list' }
                    ]
                }
            ]
        },
        {
            label: 'Scheduled',
            items: [
                { label: 'Follow-Ups', icon: 'pi pi-fw pi-clock', to: '/followups' },
                { label: 'Lead Analysis', icon: 'pi pi-fw pi-sparkles', to: '/leads/failed' }
            ]
        },
        {
            label: 'Bots / AI',
            items: [
                { label: 'Whatsapp', icon: 'pi pi-fw pi-whatsapp', to: '/bots/whatsapp' },
                { label: 'Telegram', icon: 'pi pi-fw pi-telegram', to: '/bots/telegram' },
                { label: 'Facebook', icon: 'pi pi-fw pi-facebook', to: '/bots/facebook' },
                { label: 'Email', icon: 'pi pi-fw pi-envelope', to: '/bots/email' },
                { label: 'Call', icon: 'pi pi-fw pi-phone', to: '/bots/call' }
            ]
        },
        {
            label: 'Settings',
            items: [
                { label: 'Users', icon: 'pi pi-fw pi-user-plus', to: '/users' },
                { label: 'Tenants', icon: 'pi pi-fw pi-building', to: '/tenants' }
            ]
        }
    ];
}
</script>

<template>
    <ul class="layout-menu">
        <template v-for="(item, i) in model" :key="item">
            <app-menu-item v-if="!item.separator" :item="item" :index="i"></app-menu-item>
            <li v-if="item.separator" class="menu-separator"></li>
        </template>
    </ul>
</template>

<style lang="scss" scoped></style>
