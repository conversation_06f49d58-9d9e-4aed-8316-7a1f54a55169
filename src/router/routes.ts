import AppLayout from '@/layout/AppLayout.vue';
import { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
    {
        path: '/',
        component: AppLayout,
        meta: { requiresAuth: true, maintenanceMode: true },
        children: [
            {
                path: '/dashboard',
                name: 'test-dashboard',
                component: () => import('@/views/Dashboard.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/',
                name: 'dashboard',
                component: () => import('@/entities/lead/ui/dashboard/Dashboard.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/leads/:id',
                name: 'leads',
                component: () => import('@/entities/lead/ui/components/LeadList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/bots/:id',
                name: 'bots',
                component: () => import('@/entities/lead/ui/components/BotPerformance.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/followups',
                name: 'followups',
                component: () => import('@/entities/lead/ui/components/FollowupList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/users',
                name: 'users',
                component: () => import('@/entities/user/ui/components/UserList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/tenants',
                name: 'tenants',
                component: () => import('@/entities/tenant/ui/components/TenantList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/tenants/:tenantId/users',
                name: 'tenant-users',
                component: () => import('@/entities/tenant/ui/components/TenantUserList.vue'),
                meta: { requiresAuth: true, maintenanceMode: true },
                props: true
            },
            {
                path: '/uikit/formlayout',
                name: 'formlayout',
                component: () => import('@/views/uikit/FormLayout.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/input',
                name: 'input',
                component: () => import('@/views/uikit/InputDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/button',
                name: 'button',
                component: () => import('@/views/uikit/ButtonDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/table',
                name: 'table',
                component: () => import('@/views/uikit/TableDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/list',
                name: 'list',
                component: () => import('@/views/uikit/ListDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/tree',
                name: 'tree',
                component: () => import('@/views/uikit/TreeDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/panel',
                name: 'panel',
                component: () => import('@/views/uikit/PanelsDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },

            {
                path: '/uikit/overlay',
                name: 'overlay',
                component: () => import('@/views/uikit/OverlayDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/media',
                name: 'media',
                component: () => import('@/views/uikit/MediaDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/message',
                name: 'message',
                component: () => import('@/views/uikit/MessagesDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/file',
                name: 'file',
                component: () => import('@/views/uikit/FileDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/menu',
                name: 'menu',
                component: () => import('@/views/uikit/MenuDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/charts',
                name: 'charts',
                component: () => import('@/views/uikit/ChartDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/misc',
                name: 'misc',
                component: () => import('@/views/uikit/MiscDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/uikit/timeline',
                name: 'timeline',
                component: () => import('@/views/uikit/TimelineDoc.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/pages/empty',
                name: 'empty',
                component: () => import('@/views/pages/Empty.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/pages/crud',
                name: 'crud',
                component: () => import('@/views/pages/Crud.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            },
            {
                path: '/documentation',
                name: 'documentation',
                component: () => import('@/views/pages/Documentation.vue'),
                meta: { requiresAuth: true, maintenanceMode: true }
            }
        ]
    },
    {
        path: '/landing',
        name: 'landing',
        component: () => import('@/views/pages/Landing.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/book-appointment',
        name: 'book-appointment',
        component: () => import('@/entities/appointments/ui/components/LeadAppointment.vue'),
        meta: { accessible: true, requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/notfound',
        name: 'notfound-auth',
        component: () => import('@/entities/auth/ui/components/NotFound.vue'),
        meta: { requiresAuth: true, maintenanceMode: true }
    },
    {
        path: '/notfound',
        name: 'notfound-public',
        component: () => import('@/entities/auth/ui/components/NotFound.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/entities/auth/ui/components/Login.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/register',
        name: 'register',
        component: () => import('@/entities/auth/ui/components/CompanyRegistration.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/auth/access',
        name: 'accessDenied',
        component: () => import('@/entities/auth/ui/components/Access.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    },
    {
        path: '/auth/error',
        name: 'error',
        component: () => import('@/entities/auth/ui/components/Error.vue'),
        meta: { requiresAuth: false, maintenanceMode: true }
    }
];
export default routes;
