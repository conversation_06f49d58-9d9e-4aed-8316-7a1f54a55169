import { createRouter, createWebHistory, useRouter } from 'vue-router';
import routes from './routes';
import { useAuthStore } from '@/entities/auth';

export const router = () => {
    const router = createRouter({
        scrollBehavior: () => ({ left: 0, top: 0 }),
        routes,
        history: createWebHistory()
    });

    router.beforeEach(async (to, from, next) => {
        const authStore = useAuthStore();
        await authStore.getCurrentUser();
        const requiresAuth = to.matched.some((record) => record.meta.requiresAuth);
        const accessibleByAll = to.matched.some((record) => record.meta.accessible);
        const isNotFound = to.matched.length === 0;

        const isLoggedIn = authStore.isLoggedIn();
        const isForbidden = requiresAuth && !isLoggedIn;
        const isAuthForbidden = requiresAuth && isLoggedIn;
        const isShouldNotOnPublic = !requiresAuth && isLoggedIn;

        if (isNotFound) {
            if (isLoggedIn) {
                next({ name: 'notfound-auth' });
            } else next({ name: 'notfound-public' });
        } else if (isShouldNotOnPublic) {
            if (accessibleByAll) {
                console.log('dsadsadsad');
                next();
            } else {
                next({ path: '/' });
            }
        } else if (isForbidden) {
            next({ path: '/login' });
        } else {
            next();
        }
    });

    return router;
};
