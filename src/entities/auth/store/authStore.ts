// src/stores/userStore.ts
import { defineStore } from 'pinia';
import { ref } from 'vue';
import { auth } from '@/firebase';
import { User } from 'firebase/auth';
import { User as UserApp } from '@/entities/user';
import { AuthError, usersCollection } from '@/entities/auth';
import { signInWithEmailAndPassword, sendPasswordResetEmail, signInWithPopup, GoogleAuthProvider, onAuthStateChanged } from 'firebase/auth';
import { addDoc, doc, getDoc } from 'firebase/firestore';
import { useCookie } from 'vue-cookie-next';
import { useRouter } from 'vue-router';
import { useUsersStore } from '@/entities/user';
import { requestDeleteUser, requestDeleteUsers, requestSaveUser } from '@/shared';

/**
 * Pinia store for managing authentication.
 */
export const useAuthStore = defineStore('auth', () => {
    // State
    const user = ref<User | null>(null); // Current authenticated user
    const userData = ref<any | null>(null); // Current authenticated user
    const error = ref<AuthError | null>(null); // Error state
    const loading = ref<boolean>(false); // Loading state
    const googleProvider = new GoogleAuthProvider();

    const router = useRouter();
    const userStore = useUsersStore();
    const { setCookie, removeCookie, isCookieAvailable } = useCookie();

    /**
     * Logs in a user with email and password.
     * @param {string} email - The user's email.
     * @param {string} password - The user's password.
     * @param {boolean} rememberMe - The user's rememberMe.
     */
    const login = async (email: string, password: string, rememberMe: boolean) => {
        try {
            loading.value = true;
            error.value = null;
            // Validate inputs
            if (!validateEmail(email)) throw { code: 'invalid-email', message: 'Invalid email format.' };
            if (!password) throw { code: 'missing-password', message: 'Password is required.' };

            // Sign in with Firebase
            const { user: firebaseUser } = await signInWithEmailAndPassword(auth, email, password);
            if (!firebaseUser) throw { code: 'invalid-credentials', message: 'User not found.' };
            const userLoggedIn = await userStore.getUser(firebaseUser.uid);
            user.value = { ...firebaseUser, ...userLoggedIn };

            if (rememberMe) {
                setCookie('email', email, { expire: '30d' });
                setCookie('password', password, { expire: '30d' });
                setCookie('rememberMe', 'true', { expire: '30d' });
            } else {
                removeCookie.bind({ isCookieAvailable: isCookieAvailable })('email');
                removeCookie.bind({ isCookieAvailable: isCookieAvailable })('password');
                removeCookie.bind({ isCookieAvailable: isCookieAvailable })('rememberMe');
            }
            router.push({ path: '/' });
        } catch (err: any) {
            let errorMsg = 'Unknown Error.';
            if (err instanceof Error) errorMsg = err.message;
            error.value = { code: err.code, message: errorMsg };
        } finally {
            loading.value = false;
        }
    };

    /**
     * Registers a new user.
     * @param userApp - The user object to register.
     */
    const register = async (userApp: UserApp) => {
        try {
            loading.value = true;
            error.value = null;

            // Validate inputs
            if (!validateEmail(userApp.email)) throw { code: 'invalid-email', message: 'Invalid email format.' };
            if (!userApp.id && (!userApp.password || userApp.password.length < 6))
                throw {
                    code: 'weak-password',
                    message: 'Password must be at least 6 characters long.'
                };

            // Create user in Firebase Authentication
            const firebaseUser = await requestSaveUser(userApp);

            // Update state
            user.value = firebaseUser;
        } catch (err: any) {
            error.value = { code: err.code, message: err.message };
        } finally {
            loading.value = false;
        }
    };

    /**
     * Deletes a user from Firestore.
     * @param {string} userId - The ID of the user to delete.
     */
    const deleteUser = async (userId: string) => {
        try {
            loading.value = true;
            error.value = null;

            // Delete user from Firestore
            return await requestDeleteUser({ userId: userId });
        } catch (err: any) {
            error.value = { code: err.code, message: err.message };
        } finally {
            loading.value = false;
        }
    };

    /**
     * Deletes multiple users from Firestore.
     * @param {string[]} userIds - An array of user IDs to delete.
     */
    const deleteUsers = async (userIds: string[]) => {
        try {
            loading.value = true;
            error.value = null;

            return await requestDeleteUsers({ userIds: userIds });
        } catch (err: any) {
            error.value = { code: err.code, message: err.message };
        } finally {
            loading.value = false;
        }
    };

    /**
     * Sends a password reset email to the user.
     * @param {string} email - The user's email.
     */
    const forgotPassword = async (email: string) => {
        try {
            loading.value = true;
            error.value = null;

            // Validate email
            if (!validateEmail(email)) throw { code: 'invalid-email', message: 'Invalid email format.' };

            // Send password reset email
            await sendPasswordResetEmail(auth, email);
        } catch (err: any) {
            error.value = { code: err.code, message: err.message };
        } finally {
            loading.value = false;
        }
    };

    /**
     * Logs out the current user.
     */
    const logout = async () => {
        try {
            loading.value = true;
            error.value = null;

            // Sign out from Firebase
            await auth.signOut();
            router.push({ path: '/login' });
            user.value = null;
        } catch (err: any) {
            error.value = { code: err.code, message: err.message };
        } finally {
            loading.value = false;
        }
    };

    /**
     * Logs in a user with Google OAuth.
     */
    const loginWithGoogle = async () => {
        try {
            loading.value = true;
            error.value = null;

            // Sign in with Google popup
            const result = await signInWithPopup(auth, googleProvider);
            const firebaseUser = result.user;

            // Check if user exists in Firestore; if not, create a new entry
            const userDoc = await addDoc(usersCollection, {
                id: firebaseUser.uid,
                email: firebaseUser.email || '',
                createdAt: new Date()
            });

            // Update state
            user.value = firebaseUser;
        } catch (err: any) {
            error.value = { code: err.code, message: err.message };
        } finally {
            loading.value = false;
        }
    };

    /**
     * Validates an email address.
     * @param {string} email - The email to validate.
     * @returns {boolean} - True if the email is valid, false otherwise.
     */
    const validateEmail = (email: string): boolean => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    };

    /**
     * Checks if the user is currently logged in.
     * @returns {boolean} - True if a user is logged in, false otherwise.
     */
    const isLoggedIn = (): boolean => {
        return user.value !== null;
    };

    const setUser = (userRecord: User) => {
        user.value = userRecord;
    };
    /**
     * Resets the store's state.
     */
    const resetState = () => {
        user.value = null;
        error.value = null;
        loading.value = false;
    };
    const getCurrentUser = () => {
        return new Promise((resolve, reject) => {
            const unsubscribe = onAuthStateChanged(
                auth,
                (u) => {
                    user.value = u;
                    unsubscribe();
                    resolve(u);
                },
                reject
            );
        });
    };
    const getUserData = async () => {
        if (!user.value) return null;

        const userDoc = await getDoc(doc(usersCollection, user?.value?.uid));
        const data = userDoc.data();
        userData.value = data;
        return data;
    };
    return {
        user,
        error,
        loading,
        login,
        register,
        forgotPassword,
        setUser,
        getUserData,
        getCurrentUser,
        deleteUser,
        logout,
        deleteUsers,
        loginWithGoogle,
        isLoggedIn,
        resetState
    };
});
