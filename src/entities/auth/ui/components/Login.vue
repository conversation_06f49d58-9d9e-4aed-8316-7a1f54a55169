<script setup lang="ts">
import FloatingConfigurator from '@/components/FloatingConfigurator.vue';
import { computed, inject, onMounted, ref } from 'vue';
import { useLayout } from '@/layout/composables/layout';
import { validateEmail, validateRequired } from '@/utils';
import { VueCookies } from 'vue-cookies';
import { useAuthStore } from '@/entities/auth';
import { useCookie } from 'vue-cookie-next';
import { UserFilters, useUsersStore } from '@/entities/user';

const { getCookie } = useCookie();

const { logo } = useLayout();

const authStore = useAuthStore();
const userStore = useUsersStore();

const email = ref('');
const emailError = ref('');
const password = ref('');
const passwordError = ref('');
const checked = ref(false);

const invalidLogin = computed(() => {
    return validateRequired(password.value).length > 0 || validateRequired(email.value).length > 0 || validateEmail(email.value).length > 0;
});

const validateEmailField = () => {
    if (validateRequired(email.value) || validateEmail(email.value)) {
        emailError.value = validateRequired(email.value) || validateEmail(email.value);
    } else {
        emailError.value = '';
    }
};
const validatePasswordField = () => {
    if (validateRequired(password.value)) {
        passwordError.value = validateRequired(password.value);
    } else {
        passwordError.value = '';
    }
};

// Lifecycle hook
onMounted(async () => {
    const searchFilters: UserFilters = {
        name: '',
        email: '',
        role: [],
        sort: { col: 'id', order: 'asc' },
        page: { pageSize: 10, page: 1 }
    };
    await userStore.fetchUsersFromServer(searchFilters);
    console.log(userStore.users);
    const savedEmail = getCookie('email');
    const savedPassword = getCookie('password');
    const savedRememberMe = getCookie('rememberMe') === 'true';

    if (savedRememberMe) {
        email.value = savedEmail || '';
        password.value = savedPassword || '';
        checked.value = savedRememberMe;
    }
});
</script>

<template>
    <FloatingConfigurator />
    <div class="bg-surface-50 dark:bg-surface-950 flex items-center justify-center min-h-screen min-w-[100vw] overflow-hidden">
        <div class="flex flex-col items-center justify-center">
            <div style="border-radius: 56px; padding: 0.3rem; background: linear-gradient(180deg, var(--primary-color) 10%, rgba(33, 150, 243, 0) 30%)">
                <div class="w-full bg-surface-0 dark:bg-surface-900 py-20 px-8 sm:px-20" style="border-radius: 53px">
                    <div class="text-center mb-8">
                        <img :src="`/logo/${logo}`" class="mb-8 w-16 shrink-0 mx-auto" />

                        <div class="text-surface-900 dark:text-surface-0 text-3xl font-medium mb-4">Welcome to Liftt CRM!</div>
                        <span class="text-muted-color font-medium">Sign in to continue</span>
                        <Message v-if="authStore.error && authStore.error?.message" class="flex justify-center" icon="pi pi-exclamation-circle" severity="error" size="small" variant="simple">
                            <span>{{ authStore.error.message }}</span>
                        </Message>
                    </div>

                    <div>
                        <div class="mb-8">
                            <label for="email" class="block text-surface-900 dark:text-surface-0 text-xl font-medium mb-2">Email</label>
                            <IconField>
                                <InputText @input="validateEmailField" id="email" type="text" placeholder="Email address" :invalid="emailError.length > 0" class="w-full md:w-[30rem] mb-2" v-model="email" />
                                <InputIcon v-if="emailError.length > 0" class="pi pi-info-circle error topfix" />
                            </IconField>
                            <Message v-if="emailError.length > 0" severity="error" size="small" variant="simple">
                                {{ emailError }}
                            </Message>
                        </div>
                        <div class="mb-4">
                            <label for="password" class="block text-surface-900 dark:text-surface-0 font-medium text-xl mb-2">Password</label>
                            <IconField>
                                <Password id="password" @input="validatePasswordField" v-model="password" placeholder="Password" :toggleMask="passwordError.length === 0" :invalid="passwordError.length > 0" fluid :feedback="false"></Password>
                                <InputIcon v-if="passwordError.length > 0" class="pi pi-info-circle error" />
                            </IconField>
                            <Message v-if="passwordError.length > 0" severity="error" size="small" variant="simple">
                                {{ passwordError }}
                            </Message>
                        </div>

                        <div class="flex items-center justify-between mt-2 mb-8 gap-8">
                            <div class="flex items-center">
                                <Checkbox v-model="checked" id="rememberme1" binary class="mr-2"></Checkbox>
                                <label for="rememberme1">Remember me</label>
                            </div>
                            <span class="font-medium no-underline ml-2 text-right cursor-pointer text-primary">Forgot password?</span>
                        </div>

                        <!-- <Button label="Sign In" class="w-full" as="router-link" to="/" :disabled="invalidLogin"></Button> -->
                        <Button label="Sign In" class="w-full" :disabled="invalidLogin" :loading="authStore.loading" @click="authStore.login(email, password, checked)"></Button>
                        <Divider align="center" type="dashed">
                            <span class="text-surface-600 dark:text-surface-400">or </span>
                        </Divider>
                        <Button label="Create Account" class="w-full mt-3" severity="secondary" outlined as="router-link" to="/register"></Button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.pi-eye {
    transform: scale(1.6);
    margin-right: 1rem;
}

.pi-eye-slash {
    transform: scale(1.6);
    margin-right: 1rem;
}
</style>
