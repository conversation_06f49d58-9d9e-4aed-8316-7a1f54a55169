<script setup lang="ts">
import { ref, computed } from 'vue';
import { useAuthStore } from '@/entities/auth';
import { useTenantStore } from '@/entities/tenant';
import { useRouter } from 'vue-router';
import { validateEmail, validateRequired } from '@/utils';
import FloatingConfigurator from '@/components/FloatingConfigurator.vue';
import { useLayout } from '@/layout/composables/layout';

const authStore = useAuthStore();
const tenantStore = useTenantStore();
const router = useRouter();

const { logo } = useLayout();

const companyName = ref('');
const name = ref('');
const email = ref('');
const password = ref('');

const companyNameError = ref('');
const nameError = ref('');
const emailError = ref('');
const passwordError = ref('');

const validateForm = computed(() => {
    return !companyNameError.value && !nameError.value && !emailError.value && !passwordError.value && companyName.value && name.value && email.value && password.value;
});

const validateCompanyName = () => {
    companyNameError.value = validateRequired(companyName.value);
};

const validateFullName = () => {
    nameError.value = validateRequired(name.value);
};

const validateEmailField = () => {
    emailError.value = validateRequired(email.value) || validateEmail(email.value);
};

const validatePasswordField = () => {
    passwordError.value = password.value.length < 8 ? 'Password must be at least 8 characters' : '';
};

// Rest of the handler functions remain the same
const handleRegister = async () => {
    // 1. Register the user first
    await authStore.register({
        email: email.value,
        password: password.value,
        displayName: name.value
    });

    if (authStore.user?.uid) {
        // 2. If user registration is successful, create the tenant
        await tenantStore.createTenant({
            name: companyName.value,
            email: email.value,
            ownerId: authStore.user.uid,
            phone: '', // Default value
            address: '', // Default value
            status: 'trial',
            subscriptionPlan: 'free',
            approved: false,
            createdAt: new Date(),
            updatedAt: new Date()
        });

        if (!tenantStore.error) {
            router.push('/');
        }
        // You might want to handle the tenant creation error case here
    } else {
        // You might want to handle the user registration error case here
    }
};

const handleGoogleSignUp = async () => {
    await authStore.loginWithGoogle();
    if (authStore.user?.uid) {
        // If Google sign-in is successful, create the tenant
        await tenantStore.createTenant({
            name: companyName.value,
            email: authStore.user.email || '',
            ownerId: authStore.user.uid,
            phone: '', // Default value
            address: '', // Default value
            status: 'trial',
            subscriptionPlan: 'free',
            approved: false,
            createdAt: new Date(),
            updatedAt: new Date()
        });

        if (!tenantStore.error) {
            router.push('/');
        }
        // You might want to handle the tenant creation error case here
    } else {
        // You might want to handle the user registration error case here
    }
};
</script>

<template>
    <FloatingConfigurator />
    <div class="bg-surface-50 dark:bg-surface-950 flex min-h-screen">
        <div class="flex flex-col md:flex-row w-full max-w-7xl mx-auto my-8 rounded-2xl overflow-hidden shadow-2xl">
            <!-- Form Section -->
            <div class="w-full md:w-3/5 bg-surface-0 dark:bg-surface-900 p-8">
                <div class="max-w-xl mx-auto">
                    <div class="text-center mb-8">
                        <img :src="`/logo/${logo}`" class="mb-8 w-16 shrink-0 mx-auto" />
                        <h2 class="text-3xl font-bold text-surface-900 dark:text-surface-0">Create Your Account</h2>
                        <p class="text-surface-600 dark:text-surface-400 mt-2">Start your 30-day free trial. No credit card required.</p>
                    </div>

                    <div class="space-y-6">
                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Company Name</label>
                            <InputText v-model="companyName" @input="validateCompanyName" :class="{ 'p-invalid': companyNameError }" placeholder="Your Company Name" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="companyNameError">{{ companyNameError }}</small>
                        </div>

                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Full Name</label>
                            <InputText v-model="name" @input="validateFullName" :class="{ 'p-invalid': nameError }" placeholder="John Doe" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="nameError">{{ nameError }}</small>
                        </div>

                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Email</label>
                            <InputText v-model="email" @input="validateEmailField" :class="{ 'p-invalid': emailError }" placeholder="<EMAIL>" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="emailError">{{ emailError }}</small>
                        </div>

                        <div class="field">
                            <label class="block text-surface-900 dark:text-surface-0 text-lg mb-2">Password</label>
                            <Password v-model="password" @input="validatePasswordField" :class="{ 'p-invalid': passwordError }" :feedback="true" :toggleMask="true" placeholder="Create a secure password" class="w-full" fluid autofocus />
                            <small class="text-red-500" v-if="passwordError">{{ passwordError }}</small>
                        </div>

                        <Button label="Create Account" icon="pi pi-user-plus" class="w-full p-button-primary" :disabled="!validateForm" :loading="authStore.loading" @click="handleRegister" />

                        <Divider align="center" type="dashed">
                            <span class="text-surface-600 dark:text-surface-400">or</span>
                        </Divider>

                        <Button label="Sign In" outlined as="router-link" to="/login" class="w-full p-button-outlined" @click="handleGoogleSignUp" />
                    </div>
                </div>
            </div>

            <!-- Info Section -->
            <div class="w-full md:w-2/5 bg-primary p-8 text-white">
                <div class="h-full flex flex-col justify-center">
                    <h1 class="text-4xl font-bold mb-6">Transform Your Business Today</h1>
                    <p class="text-lg mb-8 opacity-90">Join thousands of successful businesses using our platform to grow.</p>

                    <div class="space-y-4">
                        <div class="flex items-center gap-3">
                            <i class="pi pi-check-circle text-xl"></i>
                            <span>Advanced Analytics Dashboard</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="pi pi-check-circle text-xl"></i>
                            <span>Team Collaboration Tools</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <i class="pi pi-check-circle text-xl"></i>
                            <span>24/7 Premium Support</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
