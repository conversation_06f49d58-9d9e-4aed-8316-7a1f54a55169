<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import type { User } from '@/entities/user';
import { validateEmail, validateRequired } from '@/utils';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';

// Component props
const props = defineProps<{
    visible: boolean;
    user: Partial<User>;
}>();

// Component emits
const emits = defineEmits(['update:visible', 'save']);

// Local state
const internalUser = ref<Partial<User>>({});
const submitted = ref(false);
const errors = ref({
    name: '',
    email: ''
});

const roleOptions = ref([
    { label: 'Admin', value: 'tenant_admin' },
    { label: 'Sales Representative', value: 'tenant_sales' }
]);

// Watch for changes in the user prop to update the local state
watch(
    () => props.user,
    (newVal) => {
        internalUser.value = { ...newVal };
        submitted.value = false; // Reset submission state on new user
    },
    { deep: true, immediate: true }
);

// Validation functions
const validateForm = () => {
    errors.value.name = validateRequired(internalUser.value.name || '');
    errors.value.email = validateEmail(internalUser.value.email || '');
    return !errors.value.name && !errors.value.email;
};

// Handle form submission
const saveUser = () => {
    submitted.value = true;
    if (validateForm()) {
        emits('save', internalUser.value);
    }
};

// Close the dialog
const closeDialog = () => {
    emits('update:visible', false);
};
</script>

<template>
    <Dialog :visible="visible" @update:visible="closeDialog" :style="{ width: '450px' }" header="User Details" :modal="true" class="p-fluid">
        <div class="flex flex-col gap-6">
            <div class="field">
                <label for="name" class="block font-bold mb-3">Name</label>
                <IconField>
                    <InputIcon class="pi pi-user" />
                    <InputText id="name" v-model.trim="internalUser.name" required :class="{ 'p-invalid': errors.name }" autofocus fluid />
                </IconField>
                <small class="p-error" v-if="errors.name">{{ errors.name }}</small>
            </div>

            <div class="field">
                <label for="email" class="block font-bold mb-3">Email</label>
                <IconField>
                    <InputIcon class="pi pi-envelope" />
                    <InputText id="email" v-model.trim="internalUser.email" required :class="{ 'p-invalid': errors.email }" fluid />
                </IconField>
                <small class="p-error" v-if="errors.email">{{ errors.email }}</small>
            </div>

            <div class="field">
                <label for="role" class="block font-bold mb-3">Role</label>
                <Dropdown id="role" v-model="internalUser.role" :options="roleOptions" optionLabel="label" optionValue="value" placeholder="Select a Role" fluid></Dropdown>
            </div>
        </div>
        <template #footer>
            <Button label="Cancel" icon="pi pi-times" text @click="closeDialog"></Button>
            <Button label="Save" icon="pi pi-check" @click="saveUser"></Button>
        </template>
    </Dialog>
</template>

<style scoped>
.p-error {
    display: block;
    margin-top: 0.25rem;
}
</style>
