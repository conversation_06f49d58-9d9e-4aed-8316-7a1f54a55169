<script setup lang="ts">
import { ref, watch, defineProps, defineEmits } from 'vue';
import type { Tenant } from '@/entities/tenant/model';
import { validateEmail, validateRequired } from '@/utils';
import IconField from 'primevue/iconfield';
import InputIcon from 'primevue/inputicon';

// Component props
const props = defineProps<{
    visible: boolean;
    tenant: Partial<Tenant>;
}>();

// Component emits
const emits = defineEmits(['update:visible', 'save']);

// Local state
const internalTenant = ref<Partial<Tenant>>({});
const submitted = ref(false);
const errors = ref({
    name: '',
    email: ''
});

const roleOptions = ref([
    { label: 'Admin', value: 'tenant_admin' },
    { label: 'Sales Representative', value: 'tenant_sales' }
]);

// Watch for changes in the tenant prop to update the local state
watch(
    () => props.tenant,
    (newVal) => {
        internalTenant.value = { ...newVal };
        submitted.value = false; // Reset submission state on new tenant
    },
    { deep: true, immediate: true }
);

// Validation functions
const validateForm = () => {
    errors.value.name = validateRequired(internalTenant.value.name || '');
    errors.value.email = validateEmail(internalTenant.value.email || '');
    return !errors.value.name && !errors.value.email;
};

// Handle form submission
const saveTenant = () => {
    submitted.value = true;
    if (validateForm()) {
        emits('save', internalTenant.value);
    }
};

// Close the dialog
const closeDialog = () => {
    emits('update:visible', false);
};
</script>

<template>
    <Dialog :visible="visible" @update:visible="closeDialog" :style="{ width: '450px' }" header="Tenant Details" :modal="true" class="p-fluid">
        <div class="flex flex-col gap-6">
            <div class="field">
                <label for="name" class="block font-bold mb-3">Name</label>
                <IconField>
                    <InputIcon class="pi pi-building" />
                    <InputText id="name" v-model.trim="internalTenant.name" required :class="{ 'p-invalid': errors.name }" autofocus fluid />
                </IconField>
                <small class="p-error" v-if="errors.name">{{ errors.name }}</small>
            </div>

            <div class="field">
                <label for="email" class="block font-bold mb-3">Email</label>
                <IconField>
                    <InputIcon class="pi pi-envelope" />
                    <InputText id="email" v-model.trim="internalTenant.email" required :class="{ 'p-invalid': errors.email }" autofocus fluid />
                </IconField>
                <small class="p-error" v-if="errors.email">{{ errors.email }}</small>
            </div>

            <div class="field">
                <label for="phone" class="block font-bold mb-3">Phone</label>
                <IconField>
                    <InputIcon class="pi pi-phone" />
                    <InputText id="phone" v-model.trim="internalTenant.phone" autofocus fluid />
                </IconField>
            </div>

            <div class="field">
                <label for="address" class="block font-bold mb-3">Address</label>
                <IconField>
                    <InputIcon class="pi pi-map-marker" />
                    <InputText id="address" v-model.trim="internalTenant.address" autofocus fluid />
                </IconField>
            </div>

            <div class="field">
                <label for="website" class="block font-bold mb-3">Website</label>
                <IconField>
                    <InputIcon class="pi pi-globe" />
                    <InputText id="website" v-model.trim="internalTenant.website" autofocus fluid />
                </IconField>
            </div>

            <div class="field">
                <label for="status" class="block font-bold mb-3">Status</label>
                <Dropdown id="status" v-model="internalTenant.status" :options="['active', 'inactive', 'trial']" placeholder="Select a Status" autofocus fluid></Dropdown>
            </div>

            <div class="field">
                <label for="subscriptionPlan" class="block font-bold mb-3">Subscription Plan</label>
                <Dropdown id="subscriptionPlan" v-model="internalTenant.subscriptionPlan" :options="['free', 'basic', 'premium']" placeholder="Select a Plan" autofocus fluid></Dropdown>
            </div>
        </div>
        <template #footer>
            <Button label="Cancel" icon="pi pi-times" text @click="closeDialog"></Button>
            <Button label="Save" icon="pi pi-check" @click="saveTenant"></Button>
        </template>
    </Dialog>
</template>

<style scoped>
.p-error {
    display: block;
    margin-top: 0.25rem;
}
</style>
