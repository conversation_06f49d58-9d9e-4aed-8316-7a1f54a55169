import type { Timestamp } from 'firebase/firestore';

// Defines the structure for a Tenant object
export interface Tenant {
  id: string; // Unique identifier for the tenant
  name: string; // Name of the company or tenant
  email: string; // Contact email for the tenant
  phone: string; // Contact phone number
  address: string; // Physical address of the tenant
  website?: string; // Optional website URL
  status: 'active' | 'inactive' | 'trial'; // Status of the tenant's subscription
  ownerId: string; // ID of the user who owns this tenant record
  subscriptionPlan: 'free' | 'basic' | 'premium'; // Subscription plan
  approved: boolean; // Whether the tenant has been approved
  createdAt: Timestamp | Date; // Timestamp of when the tenant was created
  updatedAt: Timestamp | Date; // Timestamp of the last update
}
