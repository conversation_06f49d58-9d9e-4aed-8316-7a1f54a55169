/**
 * @interface User
 * Represents the structure of a user document.
 */
export interface User {
    id?: string;
    tenantId?: string | null;
    name: string;
    email: string;
    role: string;
    profile_pic?: string;
    createdAt?: Date;
    password?: string;
}

export interface UserFilters {
    name?: string;
    id?: string;
    email?: string;
    tenantId?: string;
    role?: string[];
    sort?: {
        col: string;
        order: 'asc' | 'desc';
    };
    page?: {
        pageSize: number;
        page: number;
    };
    dateRange?: {
        lastMessageTimeStart?: number;
        lastMessageTimeEnd?: number;
        to_followup_dateStart?: number;
        to_followup_dateEnd?: number;
    };
}
