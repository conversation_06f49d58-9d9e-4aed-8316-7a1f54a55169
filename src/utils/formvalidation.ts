/**
 * Validates that the input is not empty.
 * @param val - The value to validate.
 * @returns {string} - Error message or '' if validation passes.
 */
export const validateRequired = (val: string): string => (val?.length === 0 ? 'This field is required.' : '');

/**
 * Validates that the input is a valid email format.
 * @param val - The value to validate.
 * @returns {string} - Error message or '' if validation passes.
 */
export const validateEmail = (val: string): string => (/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val) ? '' : 'Please enter a valid email.');

/**
 * Validates that the input is a valid phone number.
 * @param val - The value to validate.
 * @returns {string} - Error message or '' if validation passes.
 */
export const validatePhoneNumber = (val: string): string => (/^\+?([0-9]{1,3})?[-. ]?(\([0-9]{3}\)|[0-9]{3})[-. ]?[0-9]{3}[-. ]?[0-9]{4}$/.test(val) ? '' : 'Please enter a valid phone number.');

/**
 * Validates that the input has a minimum length.
 * @param val - The value to validate.
 * @param min - The minimum length required.
 * @returns {string} - Error message or '' if validation passes.
 */
export const validateMinLength = (val: string, min: number): string => (val.length >= min ? '' : `Minimum length of ${min} characters is required.`);

/**
 * Validates that the input has a maximum length.
 * @param val - The value to validate.
 * @param max - The maximum length allowed.
 * @returns {string} - Error message or '' if validation passes.
 */
export const validateMaxLength = (val: string, max: number): string => (val.length <= max ? '' : `Maximum length of ${max} characters is exceeded.`);

/**
 * Validates that the input matches a specific regular expression.
 * @param val - The value to validate.
 * @param regex - The regular expression to match against.
 * @returns {string} - Error message or '' if validation passes.
 */
export const validateRegex = (val: string, regex: RegExp): string => (regex.test(val) ? '' : 'Invalid format.');

/**
 * Validates that the input is a valid number.
 * @param val - The value to validate.
 * @returns {string} - Error message or '' if validation passes.
 */
export const validateNumber = (val: string): string => (!isNaN(parseFloat(val)) && isFinite(val as any) ? '' : 'Please enter a valid number.');

/**
 * Validates that the input is within a specified numeric range.
 * @param val - The value to validate.
 * @param min - The minimum value allowed.
 * @param max - The maximum value allowed.
 * @returns {string} - Error message or '' if validation passes.
 */
export const validateRange = (val: number, min: number, max: number): string => (val >= min && val <= max ? '' : `Please enter a value between ${min} and ${max}.`);
