import { assemblyA<PERSON>pi<PERSON>ey, invokeTwilio } from '../utils';
import * as logger from 'firebase-functions/logger';
import axios from 'axios';
import * as admin from 'firebase-admin';
import { RecordingListInstancePageOptions, RecordingPage } from 'twilio/lib/rest/api/v2010/account/recording';
import { getProcessLeadAssistant, processLead } from '../analysis';
import { firestore } from 'firebase-admin';
import QuerySnapshot = firestore.QuerySnapshot;
import DocumentData = firestore.DocumentData;

export async function getCallDetails(callSid?: string | null, params?: any) {
    try {
        const twilioClient = invokeTwilio();

        let canProcess = true;
        let leadsSnapshot: QuerySnapshot<DocumentData>;
        const paramsRecord: RecordingListInstancePageOptions = { pageSize: 10 }; // Set pageSize to 10 for pagination

        if (params) {
            paramsRecord.dateCreatedBefore = new Date(params?.date_to + 'T23:59:59Z');
            paramsRecord.dateCreatedAfter = new Date(params?.date_from + 'T00:00:00Z');
        }
        if (callSid) {
            paramsRecord.callSid = callSid;
        }

        const callDetails: Record<string, any> = {}; // Store all call details here

        // Fetch the first page of recordings
        let currentPage = await twilioClient.recordings.page(paramsRecord);

        do {
            const batch = admin.firestore().batch();
            // Process each recording in the current page
            await Promise.all(
                currentPage.instances.map(async (recording) => {
                    canProcess = true;
                    if (!callSid) {
                        const leadsCollection = admin.firestore().collection('leads');
                        leadsSnapshot = await leadsCollection.where('source_id', '==', recording.callSid).where('lead_source', '==', 'call').where('lead_actions', '==', 'inquired').get();
                        // canProcess = leadsSnapshot.empty;
                    }

                    if (!callDetails[recording.callSid]) {
                        const conversationsExists = leadsSnapshot?.docs[0]?.data()!.conversations ?? [];
                        callDetails[recording.callSid] = {};
                        callDetails[recording.callSid].conversations = leadsSnapshot?.empty ? [] : conversationsExists;

                        if (!Array.isArray(callDetails[recording.callSid].conversations)) {
                            // If conversations is not an array, convert it to an array
                            callDetails[recording.callSid].conversations = Object.entries(callDetails[recording.callSid].conversations).map(([key, value]) => ({
                                // type assertion here
                                id: key,
                                ...(value as any)
                            }));
                        }
                    }

                    try {
                        const calls = await twilioClient.calls.list({
                            parentCallSid: recording.callSid,
                            limit: 1
                        });
                        calls.forEach((call) => {
                            const callJson = call.toJSON() as any;
                            delete callJson.subresourceUris;
                            const recordingJson = recording.toJSON() as any;
                            delete recordingJson.subresourceUris;
                            delete recordingJson?.encryptionDetails;

                            callDetails[recording.callSid] = {
                                ...(callDetails[recording.callSid] || {}),
                                ...callJson,
                                ...recordingJson,
                                sid: recording.callSid,
                                mediaUrl: recording.mediaUrl,
                                recordingSid: recording.sid
                            };
                        });
                    } catch (error) {
                        logger.error(`Error fetching call details for SID ${callSid}:`, error);
                        canProcess = false;
                    }

                    if (canProcess) {
                        if (recording.source == 'DialVerb' && recording.status === 'completed' && recording.duration >= '18' && recording.mediaUrl && callDetails[recording.callSid]) {
                            const recordingTime = Math.floor(recording.dateCreated.getTime() / 1000);
                            const transcribeTexts: any[] | null = await getSpeakersFromTranscription(recording.mediaUrl, recordingTime);

                            if (transcribeTexts) {
                                callDetails[recording.callSid].conversations = transcribeTexts;
                                if (callDetails[recording.callSid] && transcribeTexts.length > 0) {
                                    const callData = callDetails[recording.callSid];

                                    if (!Array.isArray(callData.conversations)) {
                                        // If conversations is not an array, convert it to an array
                                        callData.conversations = Object.entries(callData.conversations).map(([key, value]) => ({
                                            // type assertion here
                                            id: key,
                                            ...(value as any)
                                        }));
                                    }

                                    const { leadDocRef, leadData } = await handelLeadDocumentCall(callData);
                                    leadData.conversations_count = transcribeTexts?.length || 0;
                                    if (leadData.phone) await resetFollowUpDatesForCalls(leadData.phone);

                                    let toDelete = false;
                                    if (leadData.id && leadData.ai_conversation_summary === '') {
                                        const analysisAssistantId = await getProcessLeadAssistant(leadData.tenantId);

                                        const response = await processLead(leadData, analysisAssistantId, leadData.id);

                                        if (response) {
                                            leadData.phone = callData?.to;
                                            leadData.name = response?.name || '';
                                            leadData.lead_status = response?.lead_status;
                                            leadData.is_qualified = response?.is_qualified;
                                            leadData.ai_conversation_summary = response;
                                            leadData.to_followup_date = response?.follow_up_recommendation?.to_followup_date || 0;
                                            if (response?.to_delete === true) {
                                                toDelete = true;
                                            }
                                        } else {
                                            leadData.ai_conversation_summary = '';
                                        }
                                    }

                                    leadData.keywords = [
                                        ...(leadData.name ? [leadData.name] : []),
                                        ...(leadData.email ? [leadData.email] : []),
                                        ...(leadData.phone ? [leadData.phone] : []),
                                        ...(leadData.id ? [leadData.id] : []),
                                        ...(leadData.tenantId ? [leadData.tenantId] : [])
                                    ];

                                    // set latestAppointmentDate to lead
                                    leadData.latestAppointmentDate = leadData?.latestAppointmentDate || 0;

                                    // Add the record to the batch for update
                                    if (!toDelete) {
                                        logger.info(`Call ${recording.callSid} and recording ${recording.sid} has ${callData.conversations.length}.`, leadData);
                                        leadData.mark_for_deletion = false;
                                        batch.set(leadDocRef, leadData, { merge: true });
                                    } else {
                                        logger.info(`Call ${recording.callSid} and recording ${recording.sid} has ${callData.conversations.length} conversations and marked by AI for deletion due to its is irrelevant.`, leadData);
                                        leadData.mark_for_deletion = true;
                                        batch.set(leadDocRef, leadData, { merge: true });
                                    }
                                }
                            }
                        }
                    }
                })
            );

            // Fetch the next page
            const nextPage = (await currentPage.nextPage()) as RecordingPage; // Returns undefined if no next page exists
            currentPage = nextPage || undefined; // Exit loop if nextPage is undefined

            await batch.commit(); // Commit the batch after processing all recordings in the current page
        } while (currentPage);

        logger.info(`All call details processed and written to Firestore.`);
    } catch (error) {
        logger.error(`Error fetching call details for parent SID ${callSid}:`, error);
        throw error;
    }
}

const ASSEMBLYAI_BASE_URL = 'https://api.assemblyai.com/v2';

async function getSpeakersFromTranscription(audioUrl: string, recordingTime: number) {
    let utterances: any = [];

    try {
        // Step 1: Submit the transcription request
        const response = await axios.post(
            `${ASSEMBLYAI_BASE_URL}/transcript`,
            {
                audio_url: audioUrl,
                language_code: 'en_uk', // Correct for UK English
                speaker_labels: true, // Enable speaker diarization
                punctuate: true,
                format_text: true,
                filter_profanity: true,
                language_detection: false
            },
            {
                headers: {
                    authorization: assemblyAIApiKey.value(),
                    'content-type': 'application/json'
                }
            }
        );

        const transcriptId = response.data.id;

        // Step 2: Poll for the transcription result
        let transcriptStatus = '';

        const pollTranscription = async () => {
            const statusResponse = await axios.get(`${ASSEMBLYAI_BASE_URL}/transcript/${transcriptId}`, {
                headers: {
                    authorization: assemblyAIApiKey.value()
                }
            });

            transcriptStatus = statusResponse.data.status;

            if (transcriptStatus === 'completed') {
                utterances = [];
                if (statusResponse.data?.utterances) {
                    for (const utterance of statusResponse.data.utterances.sort((a: any, b: any) => a.start - b.start)) {
                        utterances.push({
                            role: 'user',
                            speaker: utterance.speaker,
                            message: utterance.text,
                            lastMessageTime: recordingTime
                        });
                    }
                }
                return utterances;
            } else if (transcriptStatus === 'error') {
                logger.error('Transcription failed:', statusResponse.data.error);
                return null;
            } else if (transcriptStatus === 'queued' || transcriptStatus === 'processing') {
                await new Promise((resolve) => setTimeout(resolve, 3000));
                // logger.error("Transcription Wait 3 seconds before polling again");
                return await pollTranscription(); // Wait 3 seconds before polling again
            }
        };
        await pollTranscription();
        return utterances;
    } catch (error) {
        logger.error('Error extracting speakers:', error);
        return null;
    }
}

export async function handelLeadDocumentCall(req: any) {
    const source_number = req?.from;
    delete req?.from;
    const { sid, callerName, to, dateCreated } = req;

    const leadsCollection = admin.firestore().collection('leads');
    const currentTime = Math.floor(dateCreated.getTime() / 1000);

    // Search for an existing lead by WaId (WhatsApp ID)
    const leadsSnapshot = await leadsCollection.where('source_id', '==', sid).where('lead_source', '==', 'call').where('lead_actions', '==', 'inquired').get();
    let leadDocRef, leadData;
    if (!leadsSnapshot.empty) {
        // Lead exists, get the reference and data
        const leadDoc = leadsSnapshot.docs[0];
        leadDocRef = leadDoc.ref;
        leadData = leadDoc.data()!;
        leadData.lead_details = { ...req, conversations: [] };
        leadData.ai_conversation_summary = '';
        leadData.number_called = source_number;
        leadData.phone = to;
        leadData.lastMessageTime = currentTime;
        leadData.address = leadData?.address ?? '';
        leadData.conversations = {
            ...(req.conversations ?? [])
        };
    } else {
        // search for the tenantId
        let tenantId = 'Lw225tYPYTssrWaZ4ipb';
        const tenantCollection = admin.firestore().collection('companies');
        const tenantSnapshot = await tenantCollection.where('call_agents.numbers', 'array-contains-any', source_number).get();
        if (tenantSnapshot.docs.length > 0) {
            tenantId = tenantSnapshot.docs[0].ref.id;
            logger.info(`Call: Found tenant ${tenantId} for number ${source_number}`);
        } else {
            logger.info(`Call: No tenant found for number ${source_number}`);
        }

        // Create a new lead document with an autogenerated ID
        leadDocRef = leadsCollection.doc(); // Auto-generate document ID
        leadData = {
            id: leadDocRef.id,
            source_id: sid,
            number_called: source_number,
            to_lead: to,
            phone: to,
            name: callerName ?? '',
            lead_details: { ...req, conversations: [] },
            lead_source: 'call', // Track the source of the lead
            lead_actions: 'inquired', // Track the source of the lead
            lastMessageTime: currentTime,
            conversations: req.conversations,
            tenantId: tenantId,
            ai_conversation_summary: '',
            address: '',
            email: ''
        };
    }
    return { leadDocRef: leadDocRef, leadData: leadData };
}

export async function resetFollowUpDatesForCalls(phone?: string | null, email?: string | null) {
    if (!phone && !email) {
        logger.info('No phone or email provided. Skipping execution.');
        return;
    }
    let query;
    query = admin.firestore().collection('leads');

    if (phone) {
        query = query.where('phone', '==', phone);
    }

    if (email) {
        query = query.where('email', '==', email);
    }

    const snapshot = await query.get();

    if (snapshot.empty) {
        logger.info('No matching documents.');
        return;
    }

    const batch = admin.firestore().batch();
    snapshot.forEach((doc) => {
        batch.set(
            doc.ref,
            {
                to_followup_date: 0
            },
            { merge: true }
        );
    });

    await batch.commit();
    logger.info(`Updated ${snapshot.size} document(s).`);
}
