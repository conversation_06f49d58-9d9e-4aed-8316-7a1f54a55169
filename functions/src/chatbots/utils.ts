// Function to handle user query using OpenAI Assistant API
import { handelLeadDocumentWhatsapp } from './whatsapp';
import { handelLeadDocumentFacebook } from './facebook';
import { handelLeadDocumentTelegram } from './telegram';
import { handelLeadDocumentEmail } from './email';
import { invokeOpenAI, tokenKey } from '../utils';
import { HttpsError } from 'firebase-functions/v2/https';
import * as admin from 'firebase-admin';
import * as logger from 'firebase-functions/logger';
import { ThreadCreateParams } from 'openai/src/resources/beta/threads/threads';
import CryptoJS from 'crypto-js';

export async function handleUserQuery(provider: string, req: any) {
    let leadDocRef: any;
    let leadData: any;
    let leadResult: any;
    const currentTime = Math.floor(Date.now() / 1000);
    let Body = '';
    let messageFormatting = '';
    switch (provider) {
        case 'whatsapp':
            leadResult = await handelLeadDocumentWhatsapp(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
            Body = req.Body;
            messageFormatting = `You are a helpful AI optimized for WhatsApp communication. Your responses (replyMessage) must align with the platform’s informal and conversational style, using emojis sparingly where appropriate to enhance engagement. The chat history will be provided as a variable, and you should always refer to it or consider it important when crafting your response. Ensure your replies are concise, clear, and formatted in short paragraphs or bullet points if necessary for readability.`;
            break;
        case 'facebook':
            leadResult = await handelLeadDocumentFacebook(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
            Body = req.message.text;
            messageFormatting = `You are a helpful AI designed to provide responses (replyMessage) tailored specifically for Facebook Messenger. You will adhere to the platform's formatting and style guidelines, ensuring your replies are concise, engaging, and suitable for casual conversations. The chat history will be provided as a variable, and you must analyze it to ensure your responses are contextually relevant and consistent with previous interactions. Always prioritize clarity and brevity while maintaining a friendly tone.`;
            break;
        case 'telegram':
            leadResult = await handelLeadDocumentTelegram(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
            Body = req.message.text;
            messageFormatting = `You are a helpful AI tailored for Telegram, a platform known for its versatility and support for rich text formatting. Your responses (replyMessage) should utilize Markdown formatting (e.g., italics , bold , code, etc.) to enhance readability and emphasize key points when needed. The chat history will be provided as a variable, and you must analyze it to maintain contextual relevance and coherence with past interactions. Balance professionalism with a conversational tone, adapting your style based on the user's preferences evident from the chat history.`;
            break;
        case 'email':
            leadResult = await handelLeadDocumentEmail(req);
            leadDocRef = leadResult.leadDocRef;
            leadData = leadResult.leadData;
            Body = `<p ><b>Subject:</b> ${req.subject}</p><br /><br />
              <p><b>Message:</b> ${req.html || req.text}</p>`;
            messageFormatting = `You are a helpful AI designed to craft responses (replyMessage) specifically for email communication in HTML format. Your replies should adhere to professional formatting standards, ensuring clarity, conciseness, and a polished tone. Use proper salutations (e.g., \`<p>Dear [Name],</p>\` or \`<p>Hi [Name],</p>\`) and closings (e.g., \`<p>Best regards,</p>\` or \`<p>Sincerely,</p>\`) to maintain professionalism. The chat history will be provided as a variable, and you must analyze it to ensure your responses are contextually relevant, consistent with previous interactions, and address all key points raised in the conversation.

The output must only include the email message in HTML format. Do not include any additional commentary, explanations, or instructions outside the email content.

Structure your response using HTML tags for readability and visual appeal:
- Use \`<p>\` tags for paragraphs.
- Use \`<strong>\` for bold text to emphasize key points.
- Use \`<em>\` for italicized text where appropriate.
- Use \`<ul>\` and \`<li>\` for bullet points if listing information.
- Avoid unnecessary jargon unless it aligns with the recipient's communication style evident from the chat history.

Ensure the email is mobile-friendly and visually balanced, avoiding overly long blocks of text.`;
            break;
    }

    messageFormatting = `Always format your final output as a valid JSON object matching this schema: {"replyMessage": "string", "preferredAppointmentDateTime": "number (optional)", "bookingIntent": "boolean (optional)"}.  Set "bookingIntent" to true if a booking link is included. If the user specifies a date/time, extract it as a Unix timestamp (seconds) in "preferredAppointmentDateTime". Output ONLY the JSON object, no extra text.`;
    if (leadData.mark_for_deletion === true) {
        throw new HttpsError('internal', 'Lead is marked for deletion, cannot process.');
    }
    leadData.lastMessageTime = currentTime;
    leadData.mark_for_deletion = false;
    if (!leadData.tenantId) {
        leadData.tenantId = 'Lw225tYPYTssrWaZ4ipb';
    }
    leadData.keywords = [...(leadData.name ? [leadData.name] : []), ...(leadData.email ? [leadData.email] : []), ...(leadData.phone ? [leadData.phone] : []), ...(leadData.id ? [leadData.id] : []), ...(leadData.tenantId ? [leadData.tenantId] : [])];

    if (!leadData.email) {
        const emailRegex = /([a-zA-Z0-9._-]+@[a-zA-Z0-9._-]+\.[a-zA-Z0-9._-]+)/;
        const foundEmails = Body.match(emailRegex);
        if (foundEmails && foundEmails.length > 0) {
            leadData.email = foundEmails[0];
        }
    }

    if (!leadData.phone) {
        const phoneRegex = /(\+44\s?7\d{3}|\(?07\d{3}\)?)\s?\d{3}\s?\d{3}/;
        const foundPhones = Body.match(phoneRegex);
        if (foundPhones && foundPhones.length > 0) {
            leadData.phone = foundPhones[0];
        }
    }

    // set defaults to lead
    leadData.latestAppointmentDate = leadData?.latestAppointmentDate ?? 0;
    leadData.address = leadData.address ?? '';
    leadData.email = leadData.email ?? '';
    leadData.name = leadData.name ?? '';
    leadData.phone = leadData.phone ?? '';

    try {
        const openai = invokeOpenAI();

        const chatmessages = [];
        chatmessages.push({
            role: 'user',
            content:
                'Customer Data: ' +
                JSON.stringify({
                    name: leadData?.name || '',
                    email: leadData?.email || '',
                    phone: leadData?.phone || '',
                    address: leadData?.address || '',
                    lead_source: leadData.lead_source
                })
        });

        chatmessages.push({
            role: 'user',
            content: Body
        });

        // check if the lead has a appointment date currently booked
        if (leadData.appointmentHistory?.length > 0) {
            // check if the appointment is in the future
            const hasFutureAppointment = leadData.appointmentHistory?.some((app: any) => {
                return app.appointmentDate >= currentTime;
            });
            if (hasFutureAppointment) {
                logger.info('Lead has a future appointment, not encouraging booking.');
                chatmessages.push({
                    role: 'user',
                    content: `The customer has an appointment booked in the future. Do not encourage them to book another appointment.`
                });
            }
        }

        // Add the conversation history
        if (leadData.conversations?.length && leadData.conversations?.length > 0) {
            let convos = [];
            const limitedMessageHistory = leadData.conversations.filter((message: any) => message.role === 'user' || message.role === 'assistant');

            // Ensure the message history is in the correct format
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
            if (limitedMessageHistory?.length) {
                convos = limitedMessageHistory
                    .filter((msg: any) => msg.content)
                    .map((msg: any) => ({
                        role: msg.role,
                        content: msg.message
                    }));
            } else {
                convos = [];
            }
            if (convos.length > 0) {
                chatmessages.push({
                    role: 'user',
                    content: `Conversations: ${JSON.stringify(convos)}`
                });
                chatmessages.push({
                    role: 'assistant',
                    content: `You are a helpful AI that remembers past interactions. You will analyze the provided chat history to ensure your responses are contextually relevant and informed by previous conversations. The chat history will be provided as a variable, and you should always refer to it or consider it important when crafting your response.`
                });
            }
        }

        // Format the response to the user
        chatmessages.push({
            role: 'assistant',
            content: messageFormatting
        });

        // Retrieve all files associated with the assistant
        const assistantFileDoc = admin.firestore().collection('companies').doc(leadData.tenantId);
        const assistantFilesSnapshot = await assistantFileDoc.get();

        const { chat } = assistantFilesSnapshot.data()!;

        leadData.conversations.push({
            role: 'user',
            message: Body,
            lastMessageTime: currentTime
        });
        if (chat.assistantId) {
            // Create a thread for the conversation
            const thread = await openai.beta.threads.create({
                messages: chatmessages as Array<ThreadCreateParams.Message>
            });

            // Run the assistant
            const run = await openai.beta.threads.runs.create(thread.id, {
                assistant_id: chat.assistantId
            });

            // Wait for the assistant's response
            let runStatus = await openai.beta.threads.runs.retrieve(thread.id, run.id);
            while (runStatus.status !== 'completed') {
                await new Promise((resolve) => setTimeout(resolve, 1000));
                runStatus = await openai.beta.threads.runs.retrieve(thread.id, run.id);
            }

            // Retrieve the assistant's response
            const messages = await openai.beta.threads.messages.list(thread.id);
            const assistantMessage = messages.data[0].content[0];
            if (assistantMessage.type === 'text') {
                leadData.conversations_count = leadData?.conversations?.length || 0;
                leadData.ai_conversation_summary = '';
                leadData.to_followup_date = 0;
                leadData.lead_actions = 'inquired';

                try {
                    // Assuming assistantMessage.text.value contains JSON enclosed in ```json ```
                    const jsonString = assistantMessage.text.value.replace(/```json\s*|\s*```/g, '');
                    logger.info('Successfully processed user query to OpenAI from assistant', assistantMessage.text.value);

                    const response = JSON.parse(jsonString);

                    if (response?.bookingIntent === true) {
                        const getUrlFromMessage = response.replyMessage.includes('https://crm.liftt.co.uk/book-appointment');
                        if (!getUrlFromMessage) {
                            return response.replyMessage;
                        }
                        const encrypted = CryptoJS.AES.encrypt(
                            JSON.stringify({
                                id: leadData.id,
                                name: leadData.name,
                                email: leadData.email,
                                phone: leadData.phone,
                                address: leadData.address,
                                tenantId: leadData.tenantId,
                                preferredAppointmentDateTime: response?.preferredAppointmentDateTime || null
                            }),
                            tokenKey
                        ).toString();
                        response.replyMessage = response.replyMessage.replace('https://crm.liftt.co.uk/book-appointment', `https://crm.liftt.co.uk/book-appointment?lead=${encrypted}`);
                    }
                    leadData.conversations.push({
                        role: 'assistant',
                        message: response.replyMessage,
                        lastMessageTime: currentTime
                    });
                    leadDocRef.set(leadData, { merge: true });
                    return response.replyMessage;
                } catch (error) {
                    leadDocRef.set(leadData, { merge: true });
                    logger.error('Failed to parse JSON response:', error);
                    return null;
                }
            } else {
                throw new HttpsError('internal', 'Failed to handle user query not a text result type.');
            }
        } else if (chat.prompt) {
            chat.prompt.prompt =
                chat.prompt.prompt +
                `## Output Requirements**  
- Format all text in **British English** (e.g., "colour", "programme", "metre").  
- Ensure strict JSON compliance:  
  - No trailing commas.  
  - All keys/values in double quotes (\`"\`).  
  - Empty optional fields as \`""\`.  
- Set \`to_delete: true\` for irrelevant leads (e.g., no contact details, no intent).  

---

## **Final JSON Schema with All Properties**

\`\`\`json
{
  "replyMessage": "string (required) - Your full response to the customer, including HTML, links, or other formatted content",  
  "preferredAppointmentDateTime": "number (optional) - Unix timestamp (seconds) if the customer specifies a date/time",  
  "bookingIntent": "boolean (optional) - Set to true if the booking link is included in \`replyMessage\`"  
}  
\`\`\``;
            chatmessages.push({
                role: 'system',
                content: chat.prompt.prompt
            });
            delete chat.prompt.prompt;
            const messagesPrompts = chatmessages.map((msg) => {
                return {
                    role: msg.role === 'assistant' ? 'system' : msg.role,
                    content: msg.content
                };
            });
            chat.prompt.messages = messagesPrompts;
            chat.prompt.response_format = { type: 'json_object' };

            // Retrieve the assistant's response
            const response = await openai.chat.completions.create(chat.prompt);
            let message = response.choices[0].message.content as string;
            if (message) {
                const jsonString = message.replace(/```json\s*|\s*```/g, '');
                logger.info(`Successfully processed user query to OpenAI from prompt`, message);
                const response = JSON.parse(jsonString);
                leadData.conversations.push({
                    role: 'assistant',
                    message: response.replyMessage,
                    lastMessageTime: currentTime
                });
                leadDocRef.set(leadData, { merge: true });
                return response.replyMessage;
            } else {
                throw new Error('Empty response from OpenAI');
            }
        } else {
            logger.error('Failed to handle user query no assistant found.');
            throw new HttpsError('internal', 'Failed to handle user query no assistant found.');
        }
    } catch (error) {
        logger.error('Error handling user query:', error);
        if (error instanceof Error) {
            leadData.conversations.push({
                role: 'error',
                message: error.message as string,
                lastMessageTime: currentTime
            });
            leadData.conversations_count = leadData?.conversations?.length || 0;
            leadDocRef.set(leadData, { merge: true });
        }
        throw new HttpsError('internal', 'Failed to handle user query.');
    }
}
