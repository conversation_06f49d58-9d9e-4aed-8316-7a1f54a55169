import * as admin from 'firebase-admin';
import { resetFollowUpDatesForCalls } from '../calls';
import { getProcessLeadAssistant, processLead } from '../analysis';
import * as logger from 'firebase-functions/logger';

export async function handelLeadDocumentVapiCall(req: any) {
    let conversations = [];
    if (req.transcripts?.length) {
        conversations = req.transcripts?.map((transcript: any) => ({
            role: transcript.user === 'user' ? 'user' : 'assistant',
            message: transcript.text,
            lastMessageTime: Math.floor(new Date(transcript.created_at).getTime() / 1000)
        }));
    }
    const customer_name = req?.variables?.customer_name || req?.variables?.name;
    const customer_email = req?.variables?.customer_email || req?.variables?.email;
    const customer_address = req?.variables?.customer_address || req?.variables?.address;
    let tenantId = req?.variables?.tenantId ?? '';
    if (!tenantId) {
        const tenantCollection = admin.firestore().collection('companies');
        const tenantSnapshot = await tenantCollection.where('call.numbers', 'array-contains-any', req.to).get();
        if (tenantSnapshot.docs.length > 0) {
            tenantId = tenantSnapshot.docs[0].ref.id;
            logger.info(`Vapi: Found tenant ${tenantId} for vapi ${req.to}`);
        } else {
            tenantId = 'Lw225tYPYTssrWaZ4ipb';
            logger.info(`Vapi: No tenant found for vapi ${req.to}`);
        }
    }
    const { transcripts, twilio_account_sid, variables, concatenated_transcript, citation_schema_id, warm_transfer_calls, ...rest } = Object.entries(req).reduce((acc: any, [key, value]) => {
        if (value !== null && value !== undefined && value !== '' && !(Array.isArray(value) && value.length === 0) && !(typeof value === 'object' && !Array.isArray(value) && Object.keys(value).length === 0)) {
            acc[key] = value;
        }
        return acc;
    }, {} as any);

    const leadDetailsArray: Record<string, any> = {
        ...rest,
        mediaUrl: rest.recording_url
    };

    const currentTime = Math.floor(Date.now() / 1000);
    const leadsCollection = admin.firestore().collection('leads');

    // Search for an existing lead by WaId (WhatsApp ID)
    const leadsSnapshot = await leadsCollection.where('source_id', '==', req.call_id).where('lead_source', '==', 'ai_call').where('lead_actions', '==', 'inquired').get();
    let leadDocRef, leadData;

    if (!leadsSnapshot.empty) {
        // Lead exists, get the reference and data
        const leadDoc = leadsSnapshot.docs[0];
        leadDocRef = leadDoc.ref;
        leadData = leadDoc.data();
    } else {
        // Create a new lead document with an autogenerated ID
        leadDocRef = leadsCollection.doc(); // Auto-generate document ID
        leadData = {
            id: leadDocRef.id,
            source_id: req.call_id,
            name: customer_name ?? '',
            email: customer_email ?? '',
            address: customer_address ?? '',
            phone: req.inbound === true ? req?.from : req?.to,
            lead_source: 'ai_call', // Track the source of the lead
            lead_actions: 'inquired', // Track the source of the lead
            lastMessageTime: currentTime,
            lead_details: leadDetailsArray,
            conversations: conversations
        };
    }

    leadData.ai_conversation_summary = '';
    leadData.lead_actions = 'inquired';
    leadData.tenantId = tenantId;
    leadData.conversations_count = conversations?.length ?? 0;
    leadData.mark_for_deletion = false;
    leadData.keywords = [...(leadData.name ? [leadData.name] : []), ...(leadData.email ? [leadData.email] : []), ...(leadData.phone ? [leadData.phone] : []), ...(leadData.id ? [leadData.id] : []), ...(leadData.tenantId ? [leadData.tenantId] : [])];

    await resetFollowUpDatesForCalls(leadData.phone);
    const analysisAssistantId = await getProcessLeadAssistant(tenantId);

    let toDelete = false;

    const response = await processLead(leadData, analysisAssistantId, leadData.id);

    if (response) {
        leadData.lead_status = response?.lead_status;
        leadData.is_qualified = response?.is_qualified;
        leadData.ai_conversation_summary = response;
        leadData.to_followup_date = response?.follow_up_recommendation?.to_followup_date ?? 0;
        if (response?.to_delete === true) {
            toDelete = true;
        }
    }

    // Add the record to the batch for update
    if (!toDelete) {
        logger.info(`AI Call ${leadData.source_id} has ${leadData.conversations.length}.`, leadData);
        leadData.mark_for_deletion = false;
    } else {
        logger.info(`AI  Call ${leadData.source_id} has ${leadData.conversations.length} conversations and marked by AI for deletion due to its is irrelevant.`, leadData);
        leadData.mark_for_deletion = true;
    }

    // set defaults to lead
    leadData.latestAppointmentDate = leadData?.latestAppointmentDate ?? 0;
    leadData.address = leadData.address ?? '';
    leadData.email = leadData.email ?? '';
    leadData.name = leadData.name ?? '';
    leadData.phone = leadData.phone ?? '';

    leadDocRef.set(leadData, { merge: true });

    return { leadDocRef: leadDocRef, leadData: leadData };
}
