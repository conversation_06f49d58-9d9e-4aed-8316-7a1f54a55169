import { onCall, HttpsError } from "firebase-functions/v2/https";
import * as admin from "firebase-admin";
import { requireAuth } from "./utils";
import { logger } from "firebase-functions";
import * as functions from "firebase-functions/v1";

export const onSaveUser = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError("unauthenticated", "Authentication required");
  }
  requireAuth(auth);
  try {
    const createUser = {
      displayName: data.name,
      email: data.email,
      password: data.password,
      emailVerified: true,
    };
    if (data.id) {
      data.UpdatedAt = Date.now();
      const user = await admin.auth().updateUser(data.id, createUser);
      admin.firestore().collection("users").doc(user.uid).set(data);
      return user;
    } else {
      data.createdAt = Date.now();
      const user = await admin.auth().createUser(createUser);
      data.id = user.uid;
      admin.firestore().collection("users").doc(user.uid).set(data);
      return user;
    }
  } catch (err) {
    logger.error("Error upserting user.", err);
    throw new HttpsError("unknown", "Error upserting user.");
  }
});

export const onDeleteUser = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError("unauthenticated", "Authentication required");
  }
  requireAuth(auth);
  try {
    const { userId } = data;
    await admin.auth().deleteUser(userId);
    const ref = admin.firestore().collection("users").doc(userId);
    await admin.firestore().recursiveDelete(ref);
    return true;
  } catch (err) {
    logger.error("Error deleting user.", err);
    throw new HttpsError("unknown", "Error deleting user.");
  }
});

export const onDeleteUsers = onCall({ cors: true }, async (request) => {
  const { data, auth } = request;
  if (!auth) {
    throw new HttpsError("unauthenticated", "Authentication required");
  }
  requireAuth(auth);
  try {
    const { userIds } = data;
    logger.info(`UesrIDS.`, userIds);
    for (const userId of userIds) {
      try {
        await admin.auth().deleteUser(userId);
      } catch (err) {
        logger.error(`Error deleting user ${userId} from auth.`, err);
      }
      try {
        const ref = admin.firestore().collection("users").doc(userId);
        await admin.firestore().recursiveDelete(ref);
      } catch (err) {
        logger.error(`Error deleting user ${userId} from firestore.`, err);
      }
    }

    return true;
  } catch (err) {
    logger.error("Error deleting users.", err);
    throw new HttpsError("unknown", "Error deleting users.");
  }
});
export const onCreateUserDoc = functions.auth.user().onCreate((user) => {
  const { uid, providerData } = user;
  const isGoogleUser = providerData.some(
    (data) => data.providerId === "google.com",
  );
  try {
    if (isGoogleUser) {
      const data = {
        id: uid,
        name: user.displayName,
        email: user.email,
        role: "user",
        createdAt: Date.now(),
      };
      admin.firestore().collection("users").doc(uid).set(data);
    }
  } catch (err) {
    logger.error("Error creating user from google.", err);
    throw new HttpsError("unknown", "Error creating user from google.");
  }
});

export const onDeleteUserDoc = functions.auth.user().onDelete(async (user) => {
  const { uid } = user;
  try {
    await admin.auth().deleteUser(uid);
    const ref = admin.firestore().collection("users").doc(uid);
    await admin.firestore().recursiveDelete(ref);
  } catch (err) {
    throw new HttpsError("unknown", "Error deleting user from google.");
    logger.error("Error deleting user from google.", err);
  }
});
