import * as logger from 'firebase-functions/logger';
import { twilioAccounSID, twilioAuthTpken, sendGridApi<PERSON>ey, requireAuth, invokeTwilio } from './utils';
import { HttpsError, onCall } from 'firebase-functions/v2/https';

// Interface for search parameters
interface SearchNumberParams {
    areaCode?: string;
    contains?: string;
    smsEnabled?: boolean;
    voiceEnabled?: boolean;
    mmsEnabled?: boolean;
    faxEnabled?: boolean;
    excludeAllAddressRequired?: boolean;
    excludeLocalAddressRequired?: boolean;
    excludeForeignAddressRequired?: boolean;
    beta?: boolean;
    nearNumber?: string;
    nearLatLong?: string;
    distance?: number;
    inPostalCode?: string;
    inRegion?: string;
    inRateCenter?: string;
    inLata?: string;
    inLocality?: string;
    limit?: number;
}

// Interface for buy number parameters
interface BuyNumberParams {
    phoneNumber: string;
    areaCode?: string;
    friendlyName?: string;
    voiceUrl?: string;
    voiceMethod?: string;
    voiceFallbackUrl?: string;
    voiceFallbackMethod?: string;
    statusCallback?: string;
    statusCallbackMethod?: string;
    voiceCallerIdLookup?: boolean;
    voiceApplicationSid?: string;
    smsUrl?: string;
    smsMethod?: string;
    smsFallbackUrl?: string;
    smsFallbackMethod?: string;
    smsApplicationSid?: string;
    accountSid?: string;
    apiVersion?: string;
    bundleSid?: string;
    identitySid?: string;
    addressSid?: string;
    emergencyStatus?: string;
    emergencyAddressSid?: string;
    trunkSid?: string;
}

// Search for available phone numbers
export const onSearchTwilioNumbers = onCall(
    {
        cors: true,
        secrets: [twilioAuthTpken, twilioAccounSID, sendGridApiKey],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;

        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);

        try {
            const searchParams: SearchNumberParams = data.searchParams || {};
            const countryCode = data.countryCode || 'US'; // Default to US

            // Validate country code
            if (!countryCode || typeof countryCode !== 'string') {
                throw new Error('Valid country code is required');
            }

            const twilio = invokeTwilio();

            // Build search parameters
            const twilioSearchParams: any = {
                limit: searchParams.limit || 20
            };

            // Add optional search parameters
            if (searchParams.areaCode) twilioSearchParams.areaCode = searchParams.areaCode;
            if (searchParams.contains) twilioSearchParams.contains = searchParams.contains;
            if (searchParams.smsEnabled !== undefined) twilioSearchParams.smsEnabled = searchParams.smsEnabled;
            if (searchParams.voiceEnabled !== undefined) twilioSearchParams.voiceEnabled = searchParams.voiceEnabled;
            if (searchParams.mmsEnabled !== undefined) twilioSearchParams.mmsEnabled = searchParams.mmsEnabled;
            if (searchParams.faxEnabled !== undefined) twilioSearchParams.faxEnabled = searchParams.faxEnabled;
            if (searchParams.excludeAllAddressRequired !== undefined) twilioSearchParams.excludeAllAddressRequired = searchParams.excludeAllAddressRequired;
            if (searchParams.excludeLocalAddressRequired !== undefined) twilioSearchParams.excludeLocalAddressRequired = searchParams.excludeLocalAddressRequired;
            if (searchParams.excludeForeignAddressRequired !== undefined) twilioSearchParams.excludeForeignAddressRequired = searchParams.excludeForeignAddressRequired;
            if (searchParams.beta !== undefined) twilioSearchParams.beta = searchParams.beta;
            if (searchParams.nearNumber) twilioSearchParams.nearNumber = searchParams.nearNumber;
            if (searchParams.nearLatLong) twilioSearchParams.nearLatLong = searchParams.nearLatLong;
            if (searchParams.distance) twilioSearchParams.distance = searchParams.distance;
            if (searchParams.inPostalCode) twilioSearchParams.inPostalCode = searchParams.inPostalCode;
            if (searchParams.inRegion) twilioSearchParams.inRegion = searchParams.inRegion;
            if (searchParams.inRateCenter) twilioSearchParams.inRateCenter = searchParams.inRateCenter;
            if (searchParams.inLata) twilioSearchParams.inLata = searchParams.inLata;
            if (searchParams.inLocality) twilioSearchParams.inLocality = searchParams.inLocality;

            logger.info('Searching for available numbers with params:', twilioSearchParams);

            // Search for available phone numbers
            const availableNumbers = await twilio.availablePhoneNumbers(countryCode).local.list(twilioSearchParams);

            const formattedNumbers = availableNumbers.map(number => ({
                phoneNumber: number.phoneNumber,
                friendlyName: number.friendlyName,
                locality: number.locality,
                region: number.region,
                postalCode: number.postalCode,
                isoCountry: number.isoCountry,
                capabilities: {
                    voice: number.capabilities?.voice || false,
                    sms: number.capabilities?.sms || false,
                    mms: number.capabilities?.mms || false,
                    fax: number.capabilities?.fax || false
                },
                addressRequirements: number.addressRequirements,
                beta: number.beta
            }));

            logger.info(`Found ${formattedNumbers.length} available numbers`);

            return {
                success: true,
                availableNumbers: formattedNumbers,
                searchParams: twilioSearchParams,
                countryCode
            };

        } catch (error) {
            logger.error('Error searching for Twilio numbers:', error);

            if (error instanceof Error) {
                // Handle specific Twilio errors
                if (error.message.includes('Invalid country code')) {
                    throw new HttpsError('invalid-argument', 'Invalid country code provided');
                }
                if (error.message.includes('Invalid area code')) {
                    throw new HttpsError('invalid-argument', 'Invalid area code provided');
                }
                throw new HttpsError('internal', `Error searching for numbers: ${error.message}`);
            }

            throw new HttpsError('internal', 'Error searching for Twilio numbers');
        }
    }
);

// Buy a Twilio phone number
export const onBuyTwilioNumber = onCall(
    {
        cors: true,
        secrets: [twilioAuthTpken, twilioAccounSID, sendGridApiKey],
        cpu: 8,
        memory: '16GiB',
        timeoutSeconds: 3600
    },
    async (request) => {
        const { data, auth } = request;

        if (!auth) {
            throw new HttpsError('unauthenticated', 'Authentication required');
        }

        requireAuth(auth);

        try {
            const buyParams: BuyNumberParams = data.buyParams || {};

            // Validate required parameters
            if (!buyParams.phoneNumber) {
                throw new Error('Phone number is required');
            }

            // Validate phone number format (basic validation)
            const phoneNumberRegex = /^\+[1-9]\d{1,14}$/;
            if (!phoneNumberRegex.test(buyParams.phoneNumber)) {
                throw new Error('Invalid phone number format. Must be in E.164 format (e.g., +1234567890)');
            }

            const twilio = invokeTwilio();

            // Build purchase parameters
            const purchaseParams: any = {
                phoneNumber: buyParams.phoneNumber
            };

            // Add optional parameters
            if (buyParams.areaCode) purchaseParams.areaCode = buyParams.areaCode;
            if (buyParams.friendlyName) purchaseParams.friendlyName = buyParams.friendlyName;
            if (buyParams.voiceUrl) purchaseParams.voiceUrl = buyParams.voiceUrl;
            if (buyParams.voiceMethod) purchaseParams.voiceMethod = buyParams.voiceMethod;
            if (buyParams.voiceFallbackUrl) purchaseParams.voiceFallbackUrl = buyParams.voiceFallbackUrl;
            if (buyParams.voiceFallbackMethod) purchaseParams.voiceFallbackMethod = buyParams.voiceFallbackMethod;
            if (buyParams.statusCallback) purchaseParams.statusCallback = buyParams.statusCallback;
            if (buyParams.statusCallbackMethod) purchaseParams.statusCallbackMethod = buyParams.statusCallbackMethod;
            if (buyParams.voiceCallerIdLookup !== undefined) purchaseParams.voiceCallerIdLookup = buyParams.voiceCallerIdLookup;
            if (buyParams.voiceApplicationSid) purchaseParams.voiceApplicationSid = buyParams.voiceApplicationSid;
            if (buyParams.smsUrl) purchaseParams.smsUrl = buyParams.smsUrl;
            if (buyParams.smsMethod) purchaseParams.smsMethod = buyParams.smsMethod;
            if (buyParams.smsFallbackUrl) purchaseParams.smsFallbackUrl = buyParams.smsFallbackUrl;
            if (buyParams.smsFallbackMethod) purchaseParams.smsFallbackMethod = buyParams.smsFallbackMethod;
            if (buyParams.smsApplicationSid) purchaseParams.smsApplicationSid = buyParams.smsApplicationSid;
            if (buyParams.accountSid) purchaseParams.accountSid = buyParams.accountSid;
            if (buyParams.apiVersion) purchaseParams.apiVersion = buyParams.apiVersion;
            if (buyParams.bundleSid) purchaseParams.bundleSid = buyParams.bundleSid;
            if (buyParams.identitySid) purchaseParams.identitySid = buyParams.identitySid;
            if (buyParams.addressSid) purchaseParams.addressSid = buyParams.addressSid;
            if (buyParams.emergencyStatus) purchaseParams.emergencyStatus = buyParams.emergencyStatus;
            if (buyParams.emergencyAddressSid) purchaseParams.emergencyAddressSid = buyParams.emergencyAddressSid;
            if (buyParams.trunkSid) purchaseParams.trunkSid = buyParams.trunkSid;

            logger.info('Attempting to purchase phone number:', buyParams.phoneNumber);

            // Purchase the phone number
            const purchasedNumber = await twilio.incomingPhoneNumbers.create(purchaseParams);

            logger.info('Successfully purchased phone number:', {
                sid: purchasedNumber.sid,
                phoneNumber: purchasedNumber.phoneNumber,
                friendlyName: purchasedNumber.friendlyName
            });

            return {
                success: true,
                purchasedNumber: {
                    sid: purchasedNumber.sid,
                    phoneNumber: purchasedNumber.phoneNumber,
                    friendlyName: purchasedNumber.friendlyName,
                    accountSid: purchasedNumber.accountSid,
                    capabilities: purchasedNumber.capabilities,
                    voiceUrl: purchasedNumber.voiceUrl,
                    voiceMethod: purchasedNumber.voiceMethod,
                    smsUrl: purchasedNumber.smsUrl,
                    smsMethod: purchasedNumber.smsMethod,
                    statusCallback: purchasedNumber.statusCallback,
                    statusCallbackMethod: purchasedNumber.statusCallbackMethod,
                    dateCreated: purchasedNumber.dateCreated,
                    dateUpdated: purchasedNumber.dateUpdated
                }
            };

        } catch (error) {
            logger.error('Error purchasing Twilio number:', error);

            if (error instanceof Error) {
                // Handle specific Twilio errors
                if (error.message.includes('not available')) {
                    throw new HttpsError('failed-precondition', 'The requested phone number is not available for purchase');
                }
                if (error.message.includes('insufficient funds')) {
                    throw new HttpsError('failed-precondition', 'Insufficient funds in Twilio account to purchase number');
                }
                if (error.message.includes('Invalid phone number')) {
                    throw new HttpsError('invalid-argument', 'Invalid phone number format');
                }
                if (error.message.includes('Address required')) {
                    throw new HttpsError('failed-precondition', 'Address verification required for this number');
                }
                throw new HttpsError('internal', `Error purchasing number: ${error.message}`);
            }

            throw new HttpsError('internal', 'Error purchasing Twilio number');
        }
    }
);
