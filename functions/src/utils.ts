import { defineSecret } from 'firebase-functions/params';
import OpenAI from 'openai';
import { Twilio } from 'twilio/lib/index';
import { google } from 'googleapis';
import { logger } from 'firebase-functions';
import { HttpsError } from 'firebase-functions/v2/https';
import type { CallableRequest } from 'firebase-functions/v2/https';

export const openAIKey = defineSecret('OPENAI_API_KEY');
export const twilioAccounSID = defineSecret('TWILIO_ACCOUNT_SID');
export const twilioAuthTpken = defineSecret('TWILIO_AUTH_TOKEN');
export const twilioWhatsappNumber = defineSecret('TWILIO_WHATSAPP_NUMBER');
export const googleServiceAccountEmail = defineSecret('GOOGLE_SERVICE_ACCOUNT_EMAIL');
export const googleServiceAccountKey = defineSecret('GOOGLE_SERVICE_ACCOUNT_KEY');
export const googleDriveFolderId = defineSecret('GOOGLE_DRIVE_FOLDER_ID');
export const telegramBotToken = defineSecret('TELEGRAM_BOT_TOKEN');
export const facebookVerifyToken = defineSecret('FACEBOOK_VERIFY_TOKEN');
export const facebookPageAccessToken = defineSecret('FACEBOOK_PAGE_ACCESS_TOKEN');
export const facebookAppId = defineSecret('FACEBOOK_APP_ID');
export const facebookAppSecret = defineSecret('FACEBOOK_APP_SECRET');
export const assemblyAIApiKey = defineSecret('ASSEMBLYAI_API_KEY');
export const sendGridApiKey = defineSecret('SENDGRID_API_KEY');
export const gravityFormConsumerKey = defineSecret('GF_CONSUMER_KEY');
export const gravityFormConsumerSecret = defineSecret('GF_CONSUMER_SECRET');

export const invokeOpenAI = () => new OpenAI({ apiKey: openAIKey.value() });
export const invokeTwilio = () => new Twilio(twilioAccounSID.value(), twilioAuthTpken.value());
export const vapiAIApiKey = defineSecret('VAPI_API_KEY');

// Initialize Google Drive API client
export const drive = google.drive('v3');

// Function to authenticate with Google Drive
export async function getDriveClient() {
    try {
        // Authenticate using JWT
        const auth = new google.auth.JWT({
            email: googleServiceAccountEmail.value(),
            key: googleServiceAccountKey.value().replace(/\\n/gm, '\n'),
            scopes: ['https://www.googleapis.com/auth/drive', 'https://www.googleapis.com/auth/calendar.events']
        });

        return auth;
    } catch (error) {
        logger.error('Error authenticating with Google Drive:', error);
        throw new Error('Failed to authenticate with Google Drive.');
    }
}

export async function getCalendarClient() {
    try {
        // Authenticate using JWT
        const auth = new google.auth.JWT({
            email: googleServiceAccountEmail.value(),
            key: googleServiceAccountKey.value().replace(/\\n/gm, '\n'),
            scopes: ['https://www.googleapis.com/auth/calendar', 'https://www.googleapis.com/auth/calendar.events', 'https://www.googleapis.com/auth/gmail.send'],
            subject: '<EMAIL>',
            keyId: '113018441059461744732'
        });

        return auth;
    } catch (error) {
        logger.error('Error authenticating with Google Calendar:', error);
        throw new Error('Failed to authenticate with Google Calendar.');
    }
}
export const calendar = google.calendar('v3');
// Refactored requireAuth function
export function requireAuth(auth: CallableRequest['auth'] | undefined | null): CallableRequest['auth'] {
    if (!auth) {
        throw new HttpsError('unauthenticated', 'Only authenticated users can invoke this function.');
    }
    return auth;
}

export const getEmailTemplate = (contentStr: string) => {
    return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Email Template</title>
    <style>
        /* Ensure basic responsiveness */
        @media only screen and (max-width: 600px) {
            table.container {
                width: 100% !important;
            }
            td.content {
                padding: 15px !important;
            }
            td.footer-links a {
                display: block;
                margin: 10px 0 !important;
            }
        }

        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f9f9f9;
            color: #333333;
        }

        table.container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            border: 1px solid #eaeaea;
            border-collapse: collapse;
        }

        .header, .footer {
            background-color: #f2f2f2;
            text-align: center;
            padding: 20px 0;
        }

        .header img {
            max-width: 150px;
            margin: 0 auto;
            display: block;
        }

        .content {
            padding: 20px;
            line-height: 1.6;
        }

        .content h1 {
            color: #ff0000;
            font-size: 24px;
            margin-bottom: 15px;
        }

        .content p {
            margin: 10px 0;
        }

        .cta-button {
            display: inline-block;
            background-color: #ff0000;
            color: #ffffff;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 5px;
            margin-top: 20px;
        }

        .footer-links {
            margin-top: 20px;
        }

        .footer-links a {
            display: inline-block;
            margin: 0 10px;
            color: #333333;
            text-decoration: none;
            font-weight: bold;
        }

        .footer-links a:hover {
            opacity: 0.8;
        }

        .operation-hours {
            margin-top: 20px;
            font-size: 12px;
            color: #666666;
            text-align: center;
        }

        .sign-off {
            margin-top: 30px;
            text-align: center;
        }

        .sign-off a {
            display: flex;
            justify-content: center;
            align-items: center;
            text-decoration: none;
            color: #333333;
            font-weight: bold;
        }

        .sign-off img {
            max-width: 50px;
            margin-right: 10px;
        }
    </style>
</head>
<body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f9f9f9; color: #333333;">
    <!-- Main Container -->
    <table class="container" width="100%" cellpadding="0" cellspacing="0" border="0" style="max-width: 600px; margin: 0 auto; background-color: #ffffff; border: 1px solid #eaeaea;">
        <!-- Header -->
        <tr>
            <td align="center" bgcolor="#f2f2f2" style="padding: 20px 0;">
                <a href="https://liftt.co.uk"  target="_blank">
                    <img src="https://liftt.co.uk/wp-content/uploads/2021/12/Liftt_Logo-red-dot.png"  alt="Liftt Logo" style="max-width: 150px; display: block; margin: 0 auto;">
                </a>
            </td>
        </tr>
        <!-- Content -->
        <tr>
            <td class="content" style="padding: 20px; line-height: 1.6;">
                ${contentStr}
            </td>
        </tr>
        <!-- Footer -->
        <tr>
            <td align="center" bgcolor="#f2f2f2" style="color: #333333; padding: 20px 0;">
                <!-- Footer Links -->
                <table cellpadding="0" cellspacing="0" border="0" style="margin-top: 20px;">
                    <tr>
                        <td class="footer-links" style="text-align: center;">
                            <a href="mailto:<EMAIL>" style="color: #333333; text-decoration: none; margin: 0 10px;">Email Us</a>
                            <a href="https://wa.me/+447700138800?text=Hello%2C%20I%20would%20like%20to%20know%20more%20about%20your%20services." style="color: #333333; text-decoration: none; margin: 0 10px;">WhatsApp</a>
                            <a href="https://t.me/lifttaibot"  style="color: #333333; text-decoration: none; margin: 0 10px;">Telegram</a>
                            <a href="https://www.facebook.com/LifttGarageDoors/"  style="color: #333333; text-decoration: none; margin: 0 10px;">Facebook</a>
                            <a href="https://www.instagram.com/liftt_roller_doors"  style="color: #333333; text-decoration: none; margin: 0 10px;">Instagram</a>
                            <a href="https://www.youtube.com/channel/UCl-EQJLfBIfnEWfD8ZVKWyQ"  style="color: #333333; text-decoration: none; margin: 0 10px;">YouTube</a>
                        </td>
                    </tr>
                </table>
                <!-- Operation Hours -->
                <p style="font-size: 12px; color: #666666; margin-top: 20px;">
                    Operation Hours: 08:00 - 17:00 | Call: <a href="tel:08000209839" style="color: #333333; text-decoration: none;">0800 020 9839</a>
                </p>
                <!-- Sign Off -->
                <div style="margin-top: 30px; text-align: center;">
                    <a href="https://liftt.co.uk"  target="_blank" style="text-decoration: none; color: #333333; font-weight: bold; display: flex; justify-content: center; align-items: center;">
                        <img src="https://liftt.co.uk/wp-content/uploads/2021/12/Liftt_Logo-red-dot.png"  alt="Liftt Logo" style="max-width: 50px; margin-right: 10px;">
                        The Liftt Team
                    </a>
                </div>
            </td>
        </tr>
    </table>
</body>
</html>`;
};
export const tokenKey = 'F7pIGUyA38LJP/5NDsPxn0yaVOxaN0LqZMOIQ5C0ubmajhx1Enp1wRicRAmANKb2\n';

export const lifttCalendarId = '<EMAIL>';

// Define secrets for Algolia credentials
export const algoliaAppId = defineSecret('ALGOLIA_APP_ID');
export const algoliaApiKey = defineSecret('ext-firestore-algolia-search-ALGOLIA_API_KEY');

export const gravityFormApiUrl = 'https://liftt.co.uk/wp-json/gf/v2';
